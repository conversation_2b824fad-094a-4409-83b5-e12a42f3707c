// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';

import 'package:week_management_system/main.dart';

void main() {
  testWidgets('App starts and shows weekly view', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const WeeklyProductivityApp());

    // Verify that the app loads
    expect(find.text('Week'), findsOneWidget);
    expect(find.text('Goals'), findsOneWidget);
    expect(find.text('Reports'), findsOneWidget);
    expect(find.text('Settings'), findsOneWidget);
  });
}
