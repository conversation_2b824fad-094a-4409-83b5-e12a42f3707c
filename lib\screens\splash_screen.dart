import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/productivity_provider.dart';
import 'weekly_view.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  bool _isInitialized = false;
  String _initializationStatus = 'Initializing...';

  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    try {
      setState(() {
        _initializationStatus = 'Setting up database...';
      });

      // Give the UI a moment to render
      await Future.delayed(const Duration(milliseconds: 100));

      if (!mounted) return;
      final provider = context.read<ProductivityProvider>();

      setState(() {
        _initializationStatus = 'Loading settings...';
      });

      // Initialize the provider
      await provider.initializeProvider();

      setState(() {
        _initializationStatus = 'Loading goals...';
      });

      await provider.loadGoals();

      setState(() {
        _initializationStatus = 'Ready!';
        _isInitialized = true;
      });

      // Small delay to show "Ready!" message
      await Future.delayed(const Duration(milliseconds: 500));

      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const WeeklyView()),
        );
      }
    } catch (e) {
      setState(() {
        _initializationStatus = 'Error: $e';
      });

      // Show error for a moment, then try to continue anyway
      await Future.delayed(const Duration(seconds: 2));

      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const WeeklyView()),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.primary,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // App Logo/Icon
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Icon(
                Icons.calendar_view_week,
                size: 60,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),

            const SizedBox(height: 32),

            // App Title
            Text(
              'Weekly Productivity',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),

            Text(
              'نظام الإنتاجية الأسبوعي',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.white.withOpacity(0.9),
              ),
            ),

            const SizedBox(height: 48),

            // Loading Indicator
            if (!_isInitialized) ...[
              const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
              const SizedBox(height: 16),
            ],

            // Status Text
            Text(
              _initializationStatus,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.white.withOpacity(0.8),
              ),
            ),

            if (_isInitialized) ...[
              const SizedBox(height: 16),
              Icon(
                Icons.check_circle,
                color: Colors.white,
                size: 32,
              ),
            ],
          ],
        ),
      ),
    );
  }
}
