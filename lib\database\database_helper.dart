import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/task.dart';
import '../models/focus_session.dart';
import '../models/daily_note.dart';
import '../models/goal.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  DatabaseHelper._internal();

  static DatabaseHelper get instance => _instance;

  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    try {
      String path = join(await getDatabasesPath(), 'productivity.db');
      print('=== INITIALIZING DATABASE ===');
      print('Database path: $path');

      final db = await openDatabase(
        path,
        version: 1,
        onCreate: _onCreate,
        onOpen: (db) {
          print('Database opened successfully');
        },
      );

      print('Database initialization complete');
      return db;
    } catch (e, stackTrace) {
      print('=== DATABASE INITIALIZATION ERROR ===');
      print('Error: $e');
      print('Stack trace: $stackTrace');
      print('=== DATABASE INITIALIZATION ERROR END ===');
      rethrow;
    }
  }

  Future<void> _onCreate(Database db, int version) async {
    try {
      print('=== CREATING DATABASE TABLES ===');

      // Tasks table
      print('Creating tasks table...');
      await db.execute('''
        CREATE TABLE tasks (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          title TEXT NOT NULL,
          description TEXT,
          date TEXT NOT NULL,
          isCompleted INTEGER NOT NULL DEFAULT 0,
          isPriority INTEGER NOT NULL DEFAULT 0,
          order INTEGER NOT NULL DEFAULT 0,
          createdAt TEXT NOT NULL,
          completedAt TEXT
        )
      ''');
      print('Tasks table created successfully');

      // Focus sessions table
      print('Creating focus_sessions table...');
      await db.execute('''
        CREATE TABLE focus_sessions (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          title TEXT NOT NULL,
          description TEXT,
          date TEXT NOT NULL,
          durationMinutes INTEGER NOT NULL,
          isCompleted INTEGER NOT NULL DEFAULT 0,
          startTime TEXT,
          endTime TEXT,
          accomplishments TEXT,
          createdAt TEXT NOT NULL
        )
      ''');
      print('Focus sessions table created successfully');

      // Daily notes table
      print('Creating daily_notes table...');
      await db.execute('''
        CREATE TABLE daily_notes (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          date TEXT NOT NULL UNIQUE,
          content TEXT NOT NULL,
          photos TEXT,
          createdAt TEXT NOT NULL,
          updatedAt TEXT NOT NULL
        )
      ''');
      print('Daily notes table created successfully');

      // Goals table
      print('Creating goals table...');
      await db.execute('''
        CREATE TABLE goals (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          title TEXT NOT NULL,
          description TEXT,
          type TEXT NOT NULL,
          status TEXT NOT NULL,
          progress REAL NOT NULL DEFAULT 0.0,
          startDate TEXT NOT NULL,
          endDate TEXT NOT NULL,
          parentGoalId INTEGER,
          createdAt TEXT NOT NULL,
          updatedAt TEXT NOT NULL,
          FOREIGN KEY (parentGoalId) REFERENCES goals (id)
        )
      ''');
      print('Goals table created successfully');

      // Settings table
      print('Creating settings table...');
      await db.execute('''
        CREATE TABLE settings (
          key TEXT PRIMARY KEY,
          value TEXT NOT NULL
        )
      ''');
      print('Settings table created successfully');

      // Insert default settings
      print('Inserting default settings...');
      await db.insert('settings', {'key': 'firstDayOfWeek', 'value': '6'}); // Saturday = 6
      await db.insert('settings', {'key': 'defaultSessionDuration', 'value': '25'});
      await db.insert('settings', {'key': 'theme', 'value': 'system'});
      print('Default settings inserted successfully');

      print('=== DATABASE TABLES CREATION COMPLETE ===');
    } catch (e, stackTrace) {
      print('=== DATABASE CREATION ERROR ===');
      print('Error: $e');
      print('Stack trace: $stackTrace');
      print('=== DATABASE CREATION ERROR END ===');
      rethrow;
    }
  }

  // Tasks CRUD operations
  Future<int> insertTask(Task task) async {
    try {
      print('=== DATABASE INSERT TASK ===');
      final db = await database;
      print('Database connection obtained');

      final taskMap = task.toMap();
      print('Task converted to map: $taskMap');

      print('Executing database insert...');
      final id = await db.insert('tasks', taskMap);
      print('Database insert successful. Generated ID: $id');

      // Verify the insert by reading it back
      print('Verifying insert by reading back...');
      final result = await db.query('tasks', where: 'id = ?', whereArgs: [id]);
      print('Verification result: $result');

      print('=== DATABASE INSERT COMPLETE ===');
      return id;
    } catch (e, stackTrace) {
      print('=== DATABASE INSERT ERROR ===');
      print('Error: $e');
      print('Stack trace: $stackTrace');
      print('=== DATABASE INSERT ERROR END ===');
      rethrow;
    }
  }

  Future<List<Task>> getTasksForDate(DateTime date) async {
    try {
      final db = await database;
      final dateStr = date.toIso8601String().split('T')[0];
      debugPrint('Getting tasks for date: $dateStr');
      final List<Map<String, dynamic>> maps = await db.query(
        'tasks',
        where: 'date LIKE ?',
        whereArgs: ['$dateStr%'],
        orderBy: 'order ASC, createdAt ASC',
      );
      debugPrint('Found ${maps.length} tasks for $dateStr');
      return List.generate(maps.length, (i) => Task.fromMap(maps[i]));
    } catch (e) {
      debugPrint('Error getting tasks for date: $e');
      return [];
    }
  }

  Future<int> updateTask(Task task) async {
    final db = await database;
    return await db.update(
      'tasks',
      task.toMap(),
      where: 'id = ?',
      whereArgs: [task.id],
    );
  }

  Future<int> deleteTask(int id) async {
    final db = await database;
    return await db.delete(
      'tasks',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Focus Sessions CRUD operations
  Future<int> insertFocusSession(FocusSession session) async {
    try {
      final db = await database;
      final sessionMap = session.toMap();
      debugPrint('Inserting focus session: $sessionMap');
      final id = await db.insert('focus_sessions', sessionMap);
      debugPrint('Focus session inserted with ID: $id');
      return id;
    } catch (e) {
      debugPrint('Error inserting focus session: $e');
      rethrow;
    }
  }

  Future<List<FocusSession>> getFocusSessionsForDate(DateTime date) async {
    final db = await database;
    final dateStr = date.toIso8601String().split('T')[0];
    final List<Map<String, dynamic>> maps = await db.query(
      'focus_sessions',
      where: 'date LIKE ?',
      whereArgs: ['$dateStr%'],
      orderBy: 'createdAt ASC',
    );
    return List.generate(maps.length, (i) => FocusSession.fromMap(maps[i]));
  }

  Future<int> updateFocusSession(FocusSession session) async {
    final db = await database;
    return await db.update(
      'focus_sessions',
      session.toMap(),
      where: 'id = ?',
      whereArgs: [session.id],
    );
  }

  Future<int> deleteFocusSession(int id) async {
    final db = await database;
    return await db.delete(
      'focus_sessions',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Daily Notes CRUD operations
  Future<int> insertOrUpdateDailyNote(DailyNote note) async {
    final db = await database;
    final dateStr = note.date.toIso8601String().split('T')[0];

    final existing = await db.query(
      'daily_notes',
      where: 'date LIKE ?',
      whereArgs: ['$dateStr%'],
    );

    if (existing.isNotEmpty) {
      return await db.update(
        'daily_notes',
        note.toMap(),
        where: 'date LIKE ?',
        whereArgs: ['$dateStr%'],
      );
    } else {
      return await db.insert('daily_notes', note.toMap());
    }
  }

  Future<DailyNote?> getDailyNoteForDate(DateTime date) async {
    final db = await database;
    final dateStr = date.toIso8601String().split('T')[0];
    final List<Map<String, dynamic>> maps = await db.query(
      'daily_notes',
      where: 'date LIKE ?',
      whereArgs: ['$dateStr%'],
    );

    if (maps.isNotEmpty) {
      return DailyNote.fromMap(maps.first);
    }
    return null;
  }

  // Goals CRUD operations
  Future<int> insertGoal(Goal goal) async {
    final db = await database;
    return await db.insert('goals', goal.toMap());
  }

  Future<List<Goal>> getGoalsByType(GoalType type) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'goals',
      where: 'type = ?',
      whereArgs: [type.name],
      orderBy: 'createdAt DESC',
    );
    return List.generate(maps.length, (i) => Goal.fromMap(maps[i]));
  }

  Future<int> updateGoal(Goal goal) async {
    final db = await database;
    return await db.update(
      'goals',
      goal.toMap(),
      where: 'id = ?',
      whereArgs: [goal.id],
    );
  }

  Future<int> deleteGoal(int id) async {
    final db = await database;
    return await db.delete(
      'goals',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Settings operations
  Future<String?> getSetting(String key) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'settings',
      where: 'key = ?',
      whereArgs: [key],
    );

    if (maps.isNotEmpty) {
      return maps.first['value'];
    }
    return null;
  }

  Future<int> setSetting(String key, String value) async {
    final db = await database;
    return await db.insert(
      'settings',
      {'key': key, 'value': value},
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  // Analytics and reporting
  Future<Map<String, dynamic>> getWeeklyStats(DateTime weekStart) async {
    try {
      final db = await database;
      final weekEnd = weekStart.add(const Duration(days: 7));

      final tasksCompleted = await db.rawQuery('''
        SELECT COUNT(*) as count FROM tasks
        WHERE isCompleted = 1 AND date >= ? AND date < ?
      ''', [weekStart.toIso8601String(), weekEnd.toIso8601String()]);

      final totalTasks = await db.rawQuery('''
        SELECT COUNT(*) as count FROM tasks
        WHERE date >= ? AND date < ?
      ''', [weekStart.toIso8601String(), weekEnd.toIso8601String()]);

      final sessionsCompleted = await db.rawQuery('''
        SELECT COUNT(*) as count, SUM(durationMinutes) as totalMinutes FROM focus_sessions
        WHERE isCompleted = 1 AND date >= ? AND date < ?
      ''', [weekStart.toIso8601String(), weekEnd.toIso8601String()]);

      return <String, dynamic>{
        'tasksCompleted': (tasksCompleted.first['count'] as int?) ?? 0,
        'totalTasks': (totalTasks.first['count'] as int?) ?? 0,
        'sessionsCompleted': (sessionsCompleted.first['count'] as int?) ?? 0,
        'totalFocusMinutes': (sessionsCompleted.first['totalMinutes'] as int?) ?? 0,
      };
    } catch (e) {
      debugPrint('Error getting weekly stats: $e');
      return <String, dynamic>{
        'tasksCompleted': 0,
        'totalTasks': 0,
        'sessionsCompleted': 0,
        'totalFocusMinutes': 0,
      };
    }
  }

  // Debug methods
  Future<void> debugDatabaseInfo() async {
    try {
      final db = await database;

      // Check if tables exist
      final tables = await db.rawQuery("SELECT name FROM sqlite_master WHERE type='table'");
      debugPrint('Database tables: ${tables.map((t) => t['name']).toList()}');

      // Count records in each table
      final taskCount = await db.rawQuery('SELECT COUNT(*) as count FROM tasks');
      final sessionCount = await db.rawQuery('SELECT COUNT(*) as count FROM focus_sessions');
      final noteCount = await db.rawQuery('SELECT COUNT(*) as count FROM daily_notes');
      final goalCount = await db.rawQuery('SELECT COUNT(*) as count FROM goals');

      debugPrint('Database record counts:');
      debugPrint('  Tasks: ${taskCount.first['count']}');
      debugPrint('  Focus Sessions: ${sessionCount.first['count']}');
      debugPrint('  Daily Notes: ${noteCount.first['count']}');
      debugPrint('  Goals: ${goalCount.first['count']}');

      // Show recent tasks
      final recentTasks = await db.query('tasks', orderBy: 'createdAt DESC', limit: 5);
      debugPrint('Recent tasks: $recentTasks');

    } catch (e) {
      debugPrint('Error getting database info: $e');
    }
  }

  Future<void> clearAllData() async {
    try {
      final db = await database;
      await db.delete('tasks');
      await db.delete('focus_sessions');
      await db.delete('daily_notes');
      await db.delete('goals');
      debugPrint('All data cleared from database');
    } catch (e) {
      debugPrint('Error clearing database: $e');
    }
  }

  Future<void> close() async {
    final db = await database;
    db.close();
  }
}
