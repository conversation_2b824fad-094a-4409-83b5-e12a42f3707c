import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../providers/productivity_provider.dart';
import 'daily_screen.dart';
import 'goals_screen.dart';
import 'reports_screen.dart';
import 'settings_screen.dart';

class WeeklyView extends StatefulWidget {
  const WeeklyView({super.key});

  @override
  State<WeeklyView> createState() => _WeeklyViewState();
}

class _WeeklyViewState extends State<WeeklyView> {
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ProductivityProvider>().loadGoals();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(
        index: _currentIndex,
        children: const [
          WeeklyOverview(),
          GoalsScreen(),
          ReportsScreen(),
          SettingsScreen(),
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,
        onTap: (index) => setState(() => _currentIndex = index),
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.calendar_view_week),
            label: 'Week',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.flag),
            label: 'Goals',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.analytics),
            label: 'Reports',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings),
            label: 'Settings',
          ),
        ],
      ),
    );
  }
}

class WeeklyOverview extends StatelessWidget {
  const WeeklyOverview({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ProductivityProvider>(
      builder: (context, provider, child) {
        return Scaffold(
          appBar: AppBar(
            title: Text(_getWeekTitle(provider.weekStart)),
            actions: [
              IconButton(
                icon: const Icon(Icons.today),
                onPressed: () {
                  provider.setSelectedDate(DateTime.now());
                },
              ),
            ],
          ),
          body: Column(
            children: [
              _buildWeekNavigation(context, provider),
              Expanded(
                child: _buildWeekGrid(context, provider),
              ),
            ],
          ),
        );
      },
    );
  }

  String _getWeekTitle(DateTime weekStart) {
    final weekEnd = weekStart.add(const Duration(days: 6));
    final formatter = DateFormat('MMM d');

    if (weekStart.month == weekEnd.month) {
      return '${formatter.format(weekStart)} - ${weekEnd.day}, ${weekStart.year}';
    } else {
      return '${formatter.format(weekStart)} - ${formatter.format(weekEnd)}, ${weekStart.year}';
    }
  }

  Widget _buildWeekNavigation(BuildContext context, ProductivityProvider provider) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            icon: const Icon(Icons.chevron_left),
            onPressed: () {
              final newDate = provider.selectedDate.subtract(const Duration(days: 7));
              provider.setSelectedDate(newDate);
            },
          ),
          Text(
            'Week ${_getWeekNumber(provider.weekStart)}',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          IconButton(
            icon: const Icon(Icons.chevron_right),
            onPressed: () {
              final newDate = provider.selectedDate.add(const Duration(days: 7));
              provider.setSelectedDate(newDate);
            },
          ),
        ],
      ),
    );
  }

  int _getWeekNumber(DateTime date) {
    final firstDayOfYear = DateTime(date.year, 1, 1);
    final daysSinceFirstDay = date.difference(firstDayOfYear).inDays;
    return (daysSinceFirstDay / 7).ceil();
  }

  Widget _buildWeekGrid(BuildContext context, ProductivityProvider provider) {
    final weekDays = provider.weekDays;

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 1,
        childAspectRatio: 4,
        mainAxisSpacing: 8,
      ),
      itemCount: weekDays.length,
      itemBuilder: (context, index) {
        final date = weekDays[index];
        return _buildDayCard(context, provider, date);
      },
    );
  }

  Widget _buildDayCard(BuildContext context, ProductivityProvider provider, DateTime date) {
    final isSelected = provider.selectedDate.day == date.day &&
                      provider.selectedDate.month == date.month &&
                      provider.selectedDate.year == date.year;

    final isToday = DateTime.now().day == date.day &&
                   DateTime.now().month == date.month &&
                   DateTime.now().year == date.year;

    final isFriday = date.weekday == DateTime.friday;

    return FutureBuilder<Map<String, int>>(
      future: _getDayStats(provider, date),
      builder: (context, snapshot) {
        final stats = snapshot.data ?? {'tasks': 0, 'completed': 0, 'sessions': 0};

        return Card(
          elevation: isSelected ? 4 : 1,
          color: isSelected
              ? Theme.of(context).colorScheme.primaryContainer
              : null,
          child: InkWell(
            onTap: () {
              provider.setSelectedDate(date);
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const DailyScreen(),
                ),
              );
            },
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Row(
                children: [
                  // Day info
                  Expanded(
                    flex: 2,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Row(
                          children: [
                            Text(
                              _getDayName(date, provider.firstDayOfWeek),
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: isToday ? FontWeight.bold : FontWeight.normal,
                                color: isToday ? Theme.of(context).colorScheme.primary : null,
                              ),
                            ),
                            if (isToday) ...[
                              const SizedBox(width: 4),
                              Icon(
                                Icons.today,
                                size: 16,
                                color: Theme.of(context).colorScheme.primary,
                              ),
                            ],
                            if (isFriday) ...[
                              const SizedBox(width: 4),
                              Icon(
                                Icons.star,
                                size: 16,
                                color: Theme.of(context).colorScheme.secondary,
                              ),
                            ],
                          ],
                        ),
                        Text(
                          DateFormat('MMM d').format(date),
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ],
                    ),
                  ),

                  // Stats
                  Expanded(
                    flex: 3,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        _buildStatChip(
                          context,
                          Icons.task_alt,
                          '${stats['completed']}/${stats['tasks']}',
                          'Tasks',
                        ),
                        _buildStatChip(
                          context,
                          Icons.timer,
                          '${stats['sessions']}',
                          'Sessions',
                        ),
                      ],
                    ),
                  ),

                  // Progress indicator
                  Container(
                    width: 8,
                    height: 40,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4),
                      color: _getProgressColor(context, stats),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Future<Map<String, int>> _getDayStats(ProductivityProvider provider, DateTime date) async {
    // This is a simplified version - in a real app, you'd query the database directly
    // For now, we'll return mock data
    return {
      'tasks': 3,
      'completed': 2,
      'sessions': 1,
    };
  }

  String _getDayName(DateTime date, int firstDayOfWeek) {
    // final arabicDays = ['الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
    final englishDays = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

    // For now, using English days. You can add language switching later
    return englishDays[date.weekday % 7];
  }

  Widget _buildStatChip(BuildContext context, IconData icon, String value, String label) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 14),
            const SizedBox(width: 2),
            Text(
              value,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            fontSize: 10,
          ),
        ),
      ],
    );
  }

  Color _getProgressColor(BuildContext context, Map<String, int> stats) {
    final tasks = stats['tasks'] ?? 0;
    final completed = stats['completed'] ?? 0;
    final sessions = stats['sessions'] ?? 0;

    if (tasks == 0) return Colors.grey;

    final taskProgress = completed / tasks;
    final hasSession = sessions > 0;

    if (taskProgress >= 0.8 && hasSession) {
      return Colors.green;
    } else if (taskProgress >= 0.5 || hasSession) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }
}
