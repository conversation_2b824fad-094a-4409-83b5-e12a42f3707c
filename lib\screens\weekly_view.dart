import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../providers/productivity_provider.dart';
import 'daily_screen.dart';
import 'goals_screen.dart';
import 'reports_screen.dart';
import 'settings_screen.dart';

class WeeklyView extends StatefulWidget {
  const WeeklyView({super.key});

  @override
  State<WeeklyView> createState() => _WeeklyViewState();
}

class _WeeklyViewState extends State<WeeklyView> {
  int _currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(
        index: _currentIndex,
        children: const [
          WeeklyOverview(),
          GoalsScreen(),
          ReportsScreen(),
          SettingsScreen(),
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,
        onTap: (index) => setState(() => _currentIndex = index),
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.calendar_view_week),
            label: 'Week',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.flag),
            label: 'Goals',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.analytics),
            label: 'Reports',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings),
            label: 'Settings',
          ),
        ],
      ),
    );
  }
}

class WeeklyOverview extends StatelessWidget {
  const WeeklyOverview({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ProductivityProvider>(
      builder: (context, provider, child) {
        return Scaffold(
          backgroundColor: const Color(0xFFF8F9FA),
          appBar: AppBar(
            elevation: 0,
            backgroundColor: Colors.transparent,
            flexibleSpace: Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [Color(0xFF667eea), Color(0xFF764ba2)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
            ),
            title: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getWeekTitle(provider.weekStart),
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
                Text(
                  'Your Weekly Journey',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.8),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
            actions: [
              Container(
                margin: const EdgeInsets.only(right: 8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: IconButton(
                  icon: const Icon(Icons.today, color: Colors.white),
                  onPressed: () {
                    provider.setSelectedDate(DateTime.now());
                    // Auto-navigate to today's page
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const DailyScreen(),
                      ),
                    );
                  },
                  tooltip: 'Go to Today',
                ),
              ),
            ],
          ),
          body: Column(
            children: [
              _buildWeekNavigation(context, provider),
              Expanded(
                child: _buildWeekGrid(context, provider),
              ),
            ],
          ),
        );
      },
    );
  }

  String _getWeekTitle(DateTime weekStart) {
    final weekEnd = weekStart.add(const Duration(days: 6));
    final formatter = DateFormat('MMM d');

    if (weekStart.month == weekEnd.month) {
      return '${formatter.format(weekStart)} - ${weekEnd.day}, ${weekStart.year}';
    } else {
      return '${formatter.format(weekStart)} - ${formatter.format(weekEnd)}, ${weekStart.year}';
    }
  }

  Widget _buildWeekNavigation(BuildContext context, ProductivityProvider provider) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            decoration: BoxDecoration(
              color: const Color(0xFF667eea).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              icon: const Icon(Icons.chevron_left, color: Color(0xFF667eea)),
              onPressed: () {
                final newDate = provider.selectedDate.subtract(const Duration(days: 7));
                provider.setSelectedDate(newDate);
              },
            ),
          ),
          Column(
            children: [
              Text(
                'Week ${_getWeekNumber(provider.weekStart)}',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2D3748),
                ),
              ),
              Text(
                '${DateFormat('MMM d').format(provider.weekStart)} - ${DateFormat('MMM d').format(provider.weekStart.add(const Duration(days: 6)))}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          Container(
            decoration: BoxDecoration(
              color: const Color(0xFF667eea).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              icon: const Icon(Icons.chevron_right, color: Color(0xFF667eea)),
              onPressed: () {
                final newDate = provider.selectedDate.add(const Duration(days: 7));
                provider.setSelectedDate(newDate);
              },
            ),
          ),
        ],
      ),
    );
  }

  int _getWeekNumber(DateTime date) {
    final firstDayOfYear = DateTime(date.year, 1, 1);
    final daysSinceFirstDay = date.difference(firstDayOfYear).inDays;
    return (daysSinceFirstDay / 7).ceil();
  }

  Widget _buildWeekGrid(BuildContext context, ProductivityProvider provider) {
    final weekDays = provider.weekDays;

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 1,
        childAspectRatio: 4,
        mainAxisSpacing: 8,
      ),
      itemCount: weekDays.length,
      itemBuilder: (context, index) {
        final date = weekDays[index];
        return _buildDayCard(context, provider, date);
      },
    );
  }

  Widget _buildDayCard(BuildContext context, ProductivityProvider provider, DateTime date) {
    final isSelected = provider.selectedDate.day == date.day &&
                      provider.selectedDate.month == date.month &&
                      provider.selectedDate.year == date.year;

    final isToday = DateTime.now().day == date.day &&
                   DateTime.now().month == date.month &&
                   DateTime.now().year == date.year;

    final isFriday = date.weekday == DateTime.friday;

    // Get gradient colors based on day
    final dayColors = _getDayGradientColors(date.weekday);

    return FutureBuilder<Map<String, int>>(
      future: _getDayStats(provider, date),
      builder: (context, snapshot) {
        final stats = snapshot.data ?? {'tasks': 0, 'completed': 0, 'sessions': 0};

        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: isToday
                ? const LinearGradient(
                    colors: [Color(0xFF667eea), Color(0xFF764ba2)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  )
                : LinearGradient(
                    colors: [Colors.white, Colors.grey[50]!],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
            boxShadow: [
              BoxShadow(
                color: isToday
                    ? const Color(0xFF667eea).withOpacity(0.3)
                    : Colors.black.withOpacity(0.1),
                blurRadius: isToday ? 15 : 8,
                offset: const Offset(0, 4),
              ),
            ],
            border: isSelected && !isToday
                ? Border.all(color: const Color(0xFF667eea), width: 2)
                : null,
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(20),
              onTap: () {
                provider.setSelectedDate(date);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const DailyScreen(),
                  ),
                );
              },
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    // Day info with colored circle
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: dayColors,
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(15),
                        boxShadow: [
                          BoxShadow(
                            color: dayColors[0].withOpacity(0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            date.day.toString(),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            _getDayAbbreviation(date.weekday),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(width: 16),

                    // Day name and info
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                _getDayName(date, provider.firstDayOfWeek),
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: isToday ? Colors.white : const Color(0xFF2D3748),
                                ),
                              ),
                              if (isToday) ...[
                                const SizedBox(width: 8),
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withOpacity(0.2),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: const Text(
                                    'TODAY',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                              if (isFriday) ...[
                                const SizedBox(width: 8),
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                  decoration: BoxDecoration(
                                    color: Colors.amber,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: const Icon(
                                    Icons.star,
                                    size: 12,
                                    color: Colors.white,
                                  ),
                                ),
                              ],
                            ],
                          ),
                          const SizedBox(height: 4),
                          Text(
                            DateFormat('MMMM d, yyyy').format(date),
                            style: TextStyle(
                              fontSize: 12,
                              color: isToday
                                  ? Colors.white.withOpacity(0.8)
                                  : Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Stats with modern design
                    Column(
                      children: [
                        _buildModernStatChip(
                          context,
                          Icons.task_alt,
                          '${stats['completed']}/${stats['tasks']}',
                          'Tasks',
                          isToday,
                        ),
                        const SizedBox(height: 8),
                        _buildModernStatChip(
                          context,
                          Icons.timer,
                          '${stats['sessions']}',
                          'Focus',
                          isToday,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Future<Map<String, int>> _getDayStats(ProductivityProvider provider, DateTime date) async {
    // This is a simplified version - in a real app, you'd query the database directly
    // For now, we'll return mock data
    return {
      'tasks': 3,
      'completed': 2,
      'sessions': 1,
    };
  }

  String _getDayName(DateTime date, int firstDayOfWeek) {
    // final arabicDays = ['الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
    final englishDays = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

    // For now, using English days. You can add language switching later
    return englishDays[date.weekday % 7];
  }

  Widget _buildStatChip(BuildContext context, IconData icon, String value, String label) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 14),
            const SizedBox(width: 2),
            Text(
              value,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            fontSize: 10,
          ),
        ),
      ],
    );
  }

  List<Color> _getDayGradientColors(int weekday) {
    switch (weekday) {
      case DateTime.monday:
        return [const Color(0xFF667eea), const Color(0xFF764ba2)];
      case DateTime.tuesday:
        return [const Color(0xFFf093fb), const Color(0xFFf5576c)];
      case DateTime.wednesday:
        return [const Color(0xFF4facfe), const Color(0xFF00f2fe)];
      case DateTime.thursday:
        return [const Color(0xFF43e97b), const Color(0xFF38f9d7)];
      case DateTime.friday:
        return [const Color(0xFFfa709a), const Color(0xFFfee140)];
      case DateTime.saturday:
        return [const Color(0xFFa8edea), const Color(0xFFfed6e3)];
      case DateTime.sunday:
        return [const Color(0xFFffecd2), const Color(0xFFfcb69f)];
      default:
        return [const Color(0xFF667eea), const Color(0xFF764ba2)];
    }
  }

  String _getDayAbbreviation(int weekday) {
    switch (weekday) {
      case DateTime.monday:
        return 'MON';
      case DateTime.tuesday:
        return 'TUE';
      case DateTime.wednesday:
        return 'WED';
      case DateTime.thursday:
        return 'THU';
      case DateTime.friday:
        return 'FRI';
      case DateTime.saturday:
        return 'SAT';
      case DateTime.sunday:
        return 'SUN';
      default:
        return 'DAY';
    }
  }

  Widget _buildModernStatChip(
    BuildContext context,
    IconData icon,
    String value,
    String label,
    bool isToday,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: isToday
            ? Colors.white.withOpacity(0.2)
            : const Color(0xFF667eea).withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isToday
              ? Colors.white.withOpacity(0.3)
              : const Color(0xFF667eea).withOpacity(0.2),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: isToday ? Colors.white : const Color(0xFF667eea),
          ),
          const SizedBox(width: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: isToday ? Colors.white : const Color(0xFF2D3748),
            ),
          ),
        ],
      ),
    );
  }

  Color _getProgressColor(BuildContext context, Map<String, int> stats) {
    final tasks = stats['tasks'] ?? 0;
    final completed = stats['completed'] ?? 0;
    final sessions = stats['sessions'] ?? 0;

    if (tasks == 0) return Colors.grey;

    final taskProgress = completed / tasks;
    final hasSession = sessions > 0;

    if (taskProgress >= 0.8 && hasSession) {
      return Colors.green;
    } else if (taskProgress >= 0.5 || hasSession) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }
}
