import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/productivity_provider.dart';

class ReportsScreen extends StatelessWidget {
  const ReportsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Reports & Analytics'),
      ),
      body: Consumer<ProductivityProvider>(
        builder: (context, provider, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildWeeklyOverview(context, provider),
                const SizedBox(height: 24),
                _buildTasksReport(context, provider),
                const SizedBox(height: 24),
                _buildFocusReport(context, provider),
                const SizedBox(height: 24),
                _buildTrendsReport(context, provider),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildWeeklyOverview(BuildContext context, ProductivityProvider provider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Weekly Overview',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            FutureBuilder<Map<String, dynamic>>(
              future: provider.getWeeklyStats(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }
                
                final stats = snapshot.data ?? {};
                final tasksCompleted = stats['tasksCompleted'] ?? 0;
                final totalTasks = stats['totalTasks'] ?? 0;
                final sessionsCompleted = stats['sessionsCompleted'] ?? 0;
                final totalFocusMinutes = stats['totalFocusMinutes'] ?? 0;
                
                return Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: _buildStatCard(
                            context,
                            'Tasks Completed',
                            '$tasksCompleted/$totalTasks',
                            Icons.task_alt,
                            totalTasks > 0 ? tasksCompleted / totalTasks : 0.0,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _buildStatCard(
                            context,
                            'Focus Sessions',
                            '$sessionsCompleted',
                            Icons.timer,
                            sessionsCompleted > 0 ? 1.0 : 0.0,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: _buildStatCard(
                            context,
                            'Focus Time',
                            '${totalFocusMinutes}min',
                            Icons.access_time,
                            totalFocusMinutes > 0 ? 1.0 : 0.0,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _buildStatCard(
                            context,
                            'Avg/Day',
                            '${(totalFocusMinutes / 7).round()}min',
                            Icons.trending_up,
                            (totalFocusMinutes / 7) > 30 ? 1.0 : (totalFocusMinutes / 7) / 30,
                          ),
                        ),
                      ],
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    double progress,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: Theme.of(context).colorScheme.primary,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(
              progress >= 0.8 ? Colors.green : 
              progress >= 0.5 ? Colors.orange : Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTasksReport(BuildContext context, ProductivityProvider provider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Tasks Report',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildReportItem(
              context,
              'Total Tasks',
              '${provider.tasks.length}',
              Icons.list,
            ),
            _buildReportItem(
              context,
              'Completed',
              '${provider.completedTasks.length}',
              Icons.check_circle,
              color: Colors.green,
            ),
            _buildReportItem(
              context,
              'Pending',
              '${provider.pendingTasks.length}',
              Icons.pending,
              color: Colors.orange,
            ),
            _buildReportItem(
              context,
              'Priority Tasks',
              '${provider.priorityTasks.length}',
              Icons.star,
              color: Colors.red,
            ),
            const SizedBox(height: 12),
            Text(
              'Completion Rate: ${(provider.taskCompletionRate * 100).round()}%',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: provider.taskCompletionRate >= 0.8 ? Colors.green : Colors.orange,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFocusReport(BuildContext context, ProductivityProvider provider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Focus Sessions Report',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildReportItem(
              context,
              'Total Sessions',
              '${provider.focusSessions.length}',
              Icons.timer,
            ),
            _buildReportItem(
              context,
              'Completed',
              '${provider.completedSessions.length}',
              Icons.check_circle,
              color: Colors.green,
            ),
            _buildReportItem(
              context,
              'Total Focus Time',
              '${provider.totalFocusMinutesToday} min',
              Icons.access_time,
              color: Colors.blue,
            ),
            const SizedBox(height: 12),
            if (provider.focusSessions.isNotEmpty)
              Text(
                'Average Session: ${(provider.totalFocusMinutesToday / provider.completedSessions.length).round()} min',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTrendsReport(BuildContext context, ProductivityProvider provider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Trends & Insights',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildInsightItem(
              context,
              'Productivity Score',
              _calculateProductivityScore(provider),
              Icons.trending_up,
            ),
            _buildInsightItem(
              context,
              'Best Day Type',
              _getBestDayType(provider),
              Icons.calendar_today,
            ),
            _buildInsightItem(
              context,
              'Focus Streak',
              '${_getFocusStreak(provider)} days',
              Icons.local_fire_department,
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Weekly Goal',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Complete 80% of daily tasks and maintain 2+ focus sessions per day',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: 8),
                  LinearProgressIndicator(
                    value: _calculateWeeklyGoalProgress(provider),
                    backgroundColor: Colors.grey[300],
                    valueColor: AlwaysStoppedAnimation<Color>(
                      Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReportItem(
    BuildContext context,
    String label,
    String value,
    IconData icon, {
    Color? color,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: color ?? Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 12),
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const Spacer(),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInsightItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.secondaryContainer,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              size: 20,
              color: Theme.of(context).colorScheme.secondary,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: Theme.of(context).textTheme.bodySmall,
                ),
                Text(
                  value,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _calculateProductivityScore(ProductivityProvider provider) {
    final taskScore = provider.taskCompletionRate * 50;
    final sessionScore = provider.completedSessions.isNotEmpty ? 30 : 0;
    final focusScore = provider.totalFocusMinutesToday > 60 ? 20 : (provider.totalFocusMinutesToday / 60) * 20;
    
    final totalScore = (taskScore + sessionScore + focusScore).round();
    return '$totalScore/100';
  }

  String _getBestDayType(ProductivityProvider provider) {
    // This would analyze historical data in a real implementation
    return provider.isFriday ? 'Planning Day' : 'Work Day';
  }

  int _getFocusStreak(ProductivityProvider provider) {
    // This would calculate consecutive days with focus sessions
    return provider.completedSessions.isNotEmpty ? 3 : 0;
  }

  double _calculateWeeklyGoalProgress(ProductivityProvider provider) {
    final taskGoal = provider.taskCompletionRate >= 0.8 ? 0.5 : provider.taskCompletionRate * 0.5;
    final sessionGoal = provider.completedSessions.length >= 2 ? 0.5 : (provider.completedSessions.length / 2) * 0.5;
    
    return taskGoal + sessionGoal;
  }
}
