import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/productivity_provider.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
      ),
      body: Consumer<ProductivityProvider>(
        builder: (context, provider, child) {
          return ListView(
            padding: const EdgeInsets.all(16),
            children: [
              _buildSectionHeader(context, 'Week Configuration'),
              _buildWeekStartSetting(context, provider),
              const SizedBox(height: 24),
              
              _buildSectionHeader(context, 'Focus Sessions'),
              _buildDefaultDurationSetting(context),
              _buildNotificationsSetting(context),
              const SizedBox(height: 24),
              
              _buildSectionHeader(context, 'Appearance'),
              _buildThemeSetting(context),
              _buildLanguageSetting(context),
              const SizedBox(height: 24),
              
              _buildSectionHeader(context, 'Data'),
              _buildBackupSetting(context),
              _buildExportSetting(context),
              _buildClearDataSetting(context),
              const SizedBox(height: 24),
              
              _buildSectionHeader(context, 'About'),
              _buildAboutItems(context),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: Theme.of(context).colorScheme.primary,
        ),
      ),
    );
  }

  Widget _buildWeekStartSetting(BuildContext context, ProductivityProvider provider) {
    final weekDays = [
      'Sunday',
      'Monday',
      'Tuesday', 
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
    ];

    return Card(
      child: ListTile(
        leading: const Icon(Icons.calendar_view_week),
        title: const Text('First Day of Week'),
        subtitle: Text('Currently: ${weekDays[provider.firstDayOfWeek]}'),
        trailing: const Icon(Icons.chevron_right),
        onTap: () {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('Select First Day of Week'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: weekDays.asMap().entries.map((entry) {
                  final index = entry.key;
                  final day = entry.value;
                  return RadioListTile<int>(
                    title: Text(day),
                    subtitle: index == 6 ? const Text('Arabic Week (Saturday)') : null,
                    value: index,
                    groupValue: provider.firstDayOfWeek,
                    onChanged: (value) {
                      if (value != null) {
                        provider.setFirstDayOfWeek(value);
                        Navigator.pop(context);
                      }
                    },
                  );
                }).toList(),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildDefaultDurationSetting(BuildContext context) {
    return Card(
      child: ListTile(
        leading: const Icon(Icons.timer),
        title: const Text('Default Session Duration'),
        subtitle: const Text('25 minutes'),
        trailing: const Icon(Icons.chevron_right),
        onTap: () {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('Select Default Duration'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [20, 25, 30, 40].map((duration) {
                  return RadioListTile<int>(
                    title: Text('$duration minutes'),
                    value: duration,
                    groupValue: 25, // TODO: Get from settings
                    onChanged: (value) {
                      // TODO: Save to settings
                      Navigator.pop(context);
                    },
                  );
                }).toList(),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildNotificationsSetting(BuildContext context) {
    return Card(
      child: SwitchListTile(
        secondary: const Icon(Icons.notifications),
        title: const Text('Session Notifications'),
        subtitle: const Text('Get notified when sessions start/end'),
        value: true, // TODO: Get from settings
        onChanged: (value) {
          // TODO: Save to settings
        },
      ),
    );
  }

  Widget _buildThemeSetting(BuildContext context) {
    return Card(
      child: ListTile(
        leading: const Icon(Icons.palette),
        title: const Text('Theme'),
        subtitle: const Text('System'),
        trailing: const Icon(Icons.chevron_right),
        onTap: () {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('Select Theme'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: ['System', 'Light', 'Dark'].map((theme) {
                  return RadioListTile<String>(
                    title: Text(theme),
                    value: theme,
                    groupValue: 'System', // TODO: Get from settings
                    onChanged: (value) {
                      // TODO: Save to settings
                      Navigator.pop(context);
                    },
                  );
                }).toList(),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildLanguageSetting(BuildContext context) {
    return Card(
      child: ListTile(
        leading: const Icon(Icons.language),
        title: const Text('Language'),
        subtitle: const Text('English'),
        trailing: const Icon(Icons.chevron_right),
        onTap: () {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('Select Language'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: ['English', 'العربية'].map((language) {
                  return RadioListTile<String>(
                    title: Text(language),
                    value: language,
                    groupValue: 'English', // TODO: Get from settings
                    onChanged: (value) {
                      // TODO: Save to settings
                      Navigator.pop(context);
                    },
                  );
                }).toList(),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildBackupSetting(BuildContext context) {
    return Card(
      child: ListTile(
        leading: const Icon(Icons.backup),
        title: const Text('Backup Data'),
        subtitle: const Text('Save your data to cloud'),
        trailing: const Icon(Icons.chevron_right),
        onTap: () {
          // TODO: Implement backup
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Backup feature coming soon!')),
          );
        },
      ),
    );
  }

  Widget _buildExportSetting(BuildContext context) {
    return Card(
      child: ListTile(
        leading: const Icon(Icons.download),
        title: const Text('Export Data'),
        subtitle: const Text('Export as CSV or JSON'),
        trailing: const Icon(Icons.chevron_right),
        onTap: () {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('Export Data'),
              content: const Text('Choose export format:'),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                    // TODO: Export as CSV
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('CSV export coming soon!')),
                    );
                  },
                  child: const Text('CSV'),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                    // TODO: Export as JSON
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('JSON export coming soon!')),
                    );
                  },
                  child: const Text('JSON'),
                ),
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Cancel'),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildClearDataSetting(BuildContext context) {
    return Card(
      child: ListTile(
        leading: const Icon(Icons.delete_forever, color: Colors.red),
        title: const Text('Clear All Data', style: TextStyle(color: Colors.red)),
        subtitle: const Text('This action cannot be undone'),
        trailing: const Icon(Icons.chevron_right),
        onTap: () {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('Clear All Data'),
              content: const Text(
                'Are you sure you want to delete all your data? This action cannot be undone.',
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                  onPressed: () {
                    Navigator.pop(context);
                    // TODO: Implement clear data
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Clear data feature coming soon!')),
                    );
                  },
                  child: const Text('Delete All'),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildAboutItems(BuildContext context) {
    return Column(
      children: [
        Card(
          child: ListTile(
            leading: const Icon(Icons.info),
            title: const Text('Version'),
            subtitle: const Text('1.0.0'),
          ),
        ),
        Card(
          child: ListTile(
            leading: const Icon(Icons.help),
            title: const Text('Help & Support'),
            trailing: const Icon(Icons.chevron_right),
            onTap: () {
              // TODO: Open help page
            },
          ),
        ),
        Card(
          child: ListTile(
            leading: const Icon(Icons.privacy_tip),
            title: const Text('Privacy Policy'),
            trailing: const Icon(Icons.chevron_right),
            onTap: () {
              // TODO: Open privacy policy
            },
          ),
        ),
        Card(
          child: ListTile(
            leading: const Icon(Icons.star),
            title: const Text('Rate App'),
            trailing: const Icon(Icons.chevron_right),
            onTap: () {
              // TODO: Open app store rating
            },
          ),
        ),
      ],
    );
  }
}
