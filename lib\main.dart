import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'screens/weekly_view.dart';
import 'providers/productivity_provider.dart';
import 'database/database_helper.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await DatabaseHelper.instance.database; // Initialize database
  runApp(const WeeklyProductivityApp());
}

class WeeklyProductivityApp extends StatelessWidget {
  const WeeklyProductivityApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => ProductivityProvider(),
      child: MaterialApp(
        title: 'Weekly Productivity System',
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(
            seedColor: Colors.blue,
            brightness: Brightness.light,
          ),
          useMaterial3: true,
        ),
        darkTheme: ThemeData(
          colorScheme: ColorScheme.fromSeed(
            seedColor: Colors.blue,
            brightness: Brightness.dark,
          ),
          useMaterial3: true,
        ),
        home: const WeeklyView(),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}
