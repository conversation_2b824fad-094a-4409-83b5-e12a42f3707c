import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:async';
import '../models/focus_session.dart';
import '../providers/productivity_provider.dart';

class FocusSessionWidget extends StatelessWidget {
  final List<FocusSession> sessions;

  const FocusSessionWidget({
    super.key,
    required this.sessions,
  });

  @override
  Widget build(BuildContext context) {
    if (sessions.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            children: [
              Icon(
                Icons.timer,
                size: 48,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                'No focus sessions planned',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Add a focus session to start deep work!',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[500],
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      children: sessions.map((session) => _buildSessionCard(context, session)).toList(),
    );
  }

  Widget _buildSessionCard(BuildContext context, FocusSession session) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: session.isInProgress ? 3 : 1,
      color: session.isInProgress 
          ? Theme.of(context).colorScheme.primaryContainer.withOpacity(0.3)
          : null,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  session.isCompleted 
                      ? Icons.check_circle 
                      : session.isInProgress 
                          ? Icons.play_circle 
                          : Icons.timer,
                  color: session.isCompleted 
                      ? Colors.green 
                      : session.isInProgress 
                          ? Theme.of(context).colorScheme.primary 
                          : Colors.grey,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    session.title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      decoration: session.isCompleted ? TextDecoration.lineThrough : null,
                    ),
                  ),
                ),
                _buildSessionStatus(context, session),
              ],
            ),
            if (session.description.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                session.description,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(
                  Icons.access_time,
                  size: 16,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Text(
                  '${session.durationMinutes} minutes',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
                if (session.isCompleted && session.actualDuration.inMinutes > 0) ...[
                  const SizedBox(width: 16),
                  Icon(
                    Icons.timer_outlined,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Actual: ${session.actualDuration.inMinutes}min',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
                const Spacer(),
                _buildSessionActions(context, session),
              ],
            ),
            if (session.accomplishments != null && session.accomplishments!.isNotEmpty) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceVariant,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Accomplishments:',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      session.accomplishments!,
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
              ),
            ],
            if (session.isInProgress) ...[
              const SizedBox(height: 12),
              _buildTimerWidget(context, session),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSessionStatus(BuildContext context, FocusSession session) {
    if (session.isCompleted) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.green.withOpacity(0.2),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.green),
        ),
        child: const Text(
          'Completed',
          style: TextStyle(
            color: Colors.green,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
      );
    } else if (session.isInProgress) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primary.withOpacity(0.2),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Theme.of(context).colorScheme.primary),
        ),
        child: Text(
          'In Progress',
          style: TextStyle(
            color: Theme.of(context).colorScheme.primary,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
      );
    } else {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.grey.withOpacity(0.2),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey),
        ),
        child: const Text(
          'Pending',
          style: TextStyle(
            color: Colors.grey,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
      );
    }
  }

  Widget _buildSessionActions(BuildContext context, FocusSession session) {
    if (session.isCompleted) {
      return PopupMenuButton<String>(
        onSelected: (value) => _handleSessionAction(context, session, value),
        itemBuilder: (context) => [
          const PopupMenuItem(
            value: 'edit',
            child: ListTile(
              leading: Icon(Icons.edit),
              title: Text('Edit'),
              contentPadding: EdgeInsets.zero,
            ),
          ),
          const PopupMenuItem(
            value: 'delete',
            child: ListTile(
              leading: Icon(Icons.delete, color: Colors.red),
              title: Text('Delete', style: TextStyle(color: Colors.red)),
              contentPadding: EdgeInsets.zero,
            ),
          ),
        ],
      );
    } else if (session.isInProgress) {
      return ElevatedButton.icon(
        onPressed: () => _completeSession(context, session),
        icon: const Icon(Icons.stop),
        label: const Text('Complete'),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.green,
          foregroundColor: Colors.white,
        ),
      );
    } else {
      return ElevatedButton.icon(
        onPressed: () => _startSession(context, session),
        icon: const Icon(Icons.play_arrow),
        label: const Text('Start'),
      );
    }
  }

  Widget _buildTimerWidget(BuildContext context, FocusSession session) {
    return _TimerWidget(session: session);
  }

  void _handleSessionAction(BuildContext context, FocusSession session, String action) {
    final provider = context.read<ProductivityProvider>();
    
    switch (action) {
      case 'edit':
        _showEditSessionDialog(context, session, provider);
        break;
      case 'delete':
        _showDeleteConfirmation(context, session, provider);
        break;
    }
  }

  void _startSession(BuildContext context, FocusSession session) {
    context.read<ProductivityProvider>().startFocusSession(session);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Started "${session.title}" - ${session.durationMinutes} minutes'),
        backgroundColor: Theme.of(context).colorScheme.primary,
      ),
    );
  }

  void _completeSession(BuildContext context, FocusSession session) {
    final accomplishmentsController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Complete Session'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Great work on "${session.title}"!'),
            const SizedBox(height: 16),
            TextField(
              controller: accomplishmentsController,
              decoration: const InputDecoration(
                labelText: 'What did you accomplish?',
                border: OutlineInputBorder(),
                hintText: 'Describe what you completed during this session...',
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              context.read<ProductivityProvider>().completeFocusSession(
                session,
                accomplishments: accomplishmentsController.text,
              );
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Session completed! 🎉'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('Complete'),
          ),
        ],
      ),
    );
  }

  void _showEditSessionDialog(BuildContext context, FocusSession session, ProductivityProvider provider) {
    final titleController = TextEditingController(text: session.title);
    final descriptionController = TextEditingController(text: session.description);
    int selectedDuration = session.durationMinutes;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Edit Focus Session'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: titleController,
                decoration: const InputDecoration(
                  labelText: 'Session Title',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 12),
              TextField(
                controller: descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
              const SizedBox(height: 12),
              DropdownButtonFormField<int>(
                value: selectedDuration,
                decoration: const InputDecoration(
                  labelText: 'Duration',
                  border: OutlineInputBorder(),
                ),
                items: const [
                  DropdownMenuItem(value: 20, child: Text('20 minutes')),
                  DropdownMenuItem(value: 25, child: Text('25 minutes')),
                  DropdownMenuItem(value: 30, child: Text('30 minutes')),
                  DropdownMenuItem(value: 40, child: Text('40 minutes')),
                ],
                onChanged: (value) => setState(() => selectedDuration = value ?? 25),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                if (titleController.text.isNotEmpty) {
                  final updatedSession = session.copyWith(
                    title: titleController.text,
                    description: descriptionController.text,
                    durationMinutes: selectedDuration,
                  );
                  provider.updateFocusSession(updatedSession);
                  Navigator.pop(context);
                }
              },
              child: const Text('Update'),
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context, FocusSession session, ProductivityProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Session'),
        content: Text('Are you sure you want to delete "${session.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            onPressed: () {
              provider.deleteFocusSession(session);
              Navigator.pop(context);
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}

class _TimerWidget extends StatefulWidget {
  final FocusSession session;

  const _TimerWidget({required this.session});

  @override
  State<_TimerWidget> createState() => _TimerWidgetState();
}

class _TimerWidgetState extends State<_TimerWidget> {
  Timer? _timer;
  Duration _elapsed = Duration.zero;

  @override
  void initState() {
    super.initState();
    if (widget.session.startTime != null) {
      _elapsed = DateTime.now().difference(widget.session.startTime!);
    }
    _startTimer();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (widget.session.startTime != null) {
        setState(() {
          _elapsed = DateTime.now().difference(widget.session.startTime!);
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final targetDuration = Duration(minutes: widget.session.durationMinutes);
    final progress = _elapsed.inSeconds / targetDuration.inSeconds;
    final remainingTime = targetDuration - _elapsed;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Text(
            'Session in Progress',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          CircularProgressIndicator(
            value: progress.clamp(0.0, 1.0),
            strokeWidth: 8,
            backgroundColor: Colors.grey[300],
          ),
          const SizedBox(height: 12),
          Text(
            remainingTime.isNegative 
                ? 'Overtime: ${_formatDuration(_elapsed - targetDuration)}'
                : 'Remaining: ${_formatDuration(remainingTime)}',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: remainingTime.isNegative ? Colors.red : null,
            ),
          ),
          Text(
            'Elapsed: ${_formatDuration(_elapsed)}',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ],
      ),
    );
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes.abs();
    final seconds = (duration.inSeconds.abs() % 60);
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }
}
