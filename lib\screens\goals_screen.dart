import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/productivity_provider.dart';
import '../models/goal.dart';

class GoalsScreen extends StatelessWidget {
  const GoalsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Goals'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showAddGoalDialog(context),
          ),
        ],
      ),
      body: Consumer<ProductivityProvider>(
        builder: (context, provider, child) {
          final yearlyGoals = provider.goals.where((g) => g.type == GoalType.yearly).toList();
          final monthlyGoals = provider.goals.where((g) => g.type == GoalType.monthly).toList();
          final weeklyGoals = provider.goals.where((g) => g.type == GoalType.weekly).toList();

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildGoalSection(context, 'Yearly Goals', yearlyGoals, GoalType.yearly),
                const SizedBox(height: 24),
                _buildGoalSection(context, 'Monthly Goals', monthlyGoals, GoalType.monthly),
                const SizedBox(height: 24),
                _buildGoalSection(context, 'Weekly Goals', weeklyGoals, GoalType.weekly),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildGoalSection(BuildContext context, String title, List<Goal> goals, GoalType type) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: () => _showAddGoalDialog(context, type: type),
            ),
          ],
        ),
        const SizedBox(height: 8),
        if (goals.isEmpty)
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Center(
                child: Text(
                  'No ${title.toLowerCase()} yet',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey,
                  ),
                ),
              ),
            ),
          )
        else
          ...goals.map((goal) => _buildGoalCard(context, goal)),
      ],
    );
  }

  Widget _buildGoalCard(BuildContext context, Goal goal) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    goal.title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                _buildStatusChip(context, goal.status),
              ],
            ),
            if (goal.description.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                goal.description,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: LinearProgressIndicator(
                    value: goal.progress,
                    backgroundColor: Colors.grey[300],
                    valueColor: AlwaysStoppedAnimation<Color>(
                      _getProgressColor(goal.progress),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  '${goal.progressPercentage}%',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  size: 16,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Text(
                  '${goal.startDate.day}/${goal.startDate.month} - ${goal.endDate.day}/${goal.endDate.month}',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.edit, size: 20),
                  onPressed: () => _showEditGoalDialog(context, goal),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(BuildContext context, GoalStatus status) {
    Color color;
    String text;
    
    switch (status) {
      case GoalStatus.onTrack:
        color = Colors.green;
        text = 'On Track';
        break;
      case GoalStatus.behind:
        color = Colors.orange;
        text = 'Behind';
        break;
      case GoalStatus.completed:
        color = Colors.blue;
        text = 'Completed';
        break;
      case GoalStatus.paused:
        color = Colors.grey;
        text = 'Paused';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Color _getProgressColor(double progress) {
    if (progress >= 0.8) return Colors.green;
    if (progress >= 0.5) return Colors.orange;
    return Colors.red;
  }

  void _showAddGoalDialog(BuildContext context, {GoalType? type}) {
    _showGoalDialog(context, type: type);
  }

  void _showEditGoalDialog(BuildContext context, Goal goal) {
    _showGoalDialog(context, goal: goal);
  }

  void _showGoalDialog(BuildContext context, {Goal? goal, GoalType? type}) {
    final titleController = TextEditingController(text: goal?.title ?? '');
    final descriptionController = TextEditingController(text: goal?.description ?? '');
    GoalType selectedType = goal?.type ?? type ?? GoalType.weekly;
    GoalStatus selectedStatus = goal?.status ?? GoalStatus.onTrack;
    double progress = goal?.progress ?? 0.0;
    DateTime startDate = goal?.startDate ?? DateTime.now();
    DateTime endDate = goal?.endDate ?? DateTime.now().add(const Duration(days: 7));

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text(goal == null ? 'Add Goal' : 'Edit Goal'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: titleController,
                  decoration: const InputDecoration(
                    labelText: 'Goal Title',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 12),
                TextField(
                  controller: descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'Description',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                ),
                const SizedBox(height: 12),
                DropdownButtonFormField<GoalType>(
                  value: selectedType,
                  decoration: const InputDecoration(
                    labelText: 'Type',
                    border: OutlineInputBorder(),
                  ),
                  items: GoalType.values.map((type) => DropdownMenuItem(
                    value: type,
                    child: Text(type.name.toUpperCase()),
                  )).toList(),
                  onChanged: (value) => setState(() => selectedType = value!),
                ),
                const SizedBox(height: 12),
                DropdownButtonFormField<GoalStatus>(
                  value: selectedStatus,
                  decoration: const InputDecoration(
                    labelText: 'Status',
                    border: OutlineInputBorder(),
                  ),
                  items: GoalStatus.values.map((status) => DropdownMenuItem(
                    value: status,
                    child: Text(status.name.replaceAll('_', ' ').toUpperCase()),
                  )).toList(),
                  onChanged: (value) => setState(() => selectedStatus = value!),
                ),
                const SizedBox(height: 12),
                Text('Progress: ${(progress * 100).round()}%'),
                Slider(
                  value: progress,
                  onChanged: (value) => setState(() => progress = value),
                  divisions: 10,
                  label: '${(progress * 100).round()}%',
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                if (titleController.text.isNotEmpty) {
                  final now = DateTime.now();
                  final newGoal = Goal(
                    id: goal?.id,
                    title: titleController.text,
                    description: descriptionController.text,
                    type: selectedType,
                    status: selectedStatus,
                    progress: progress,
                    startDate: startDate,
                    endDate: endDate,
                    createdAt: goal?.createdAt ?? now,
                    updatedAt: now,
                  );

                  final provider = context.read<ProductivityProvider>();
                  if (goal == null) {
                    provider.addGoal(newGoal);
                  } else {
                    provider.updateGoal(newGoal);
                  }
                  Navigator.pop(context);
                }
              },
              child: Text(goal == null ? 'Add' : 'Update'),
            ),
          ],
        ),
      ),
    );
  }
}
