import 'package:flutter/material.dart';
import '../models/task.dart';
import '../models/focus_session.dart';
import '../models/daily_note.dart';
import '../models/goal.dart';
import '../database/database_helper.dart';

class ProductivityProvider extends ChangeNotifier {
  final DatabaseHelper _dbHelper = DatabaseHelper.instance;
  
  // Current selected date
  DateTime _selectedDate = DateTime.now();
  DateTime get selectedDate => _selectedDate;

  // Week start day (0 = Sunday, 6 = Saturday)
  int _firstDayOfWeek = 6; // Saturday by default (Arabic week)
  int get firstDayOfWeek => _firstDayOfWeek;

  // Data lists
  List<Task> _tasks = [];
  List<FocusSession> _focusSessions = [];
  DailyNote? _dailyNote;
  List<Goal> _goals = [];

  // Getters
  List<Task> get tasks => _tasks;
  List<FocusSession> get focusSessions => _focusSessions;
  DailyNote? get dailyNote => _dailyNote;
  List<Goal> get goals => _goals;

  // Loading states
  bool _isLoading = false;
  bool get isLoading => _isLoading;

  ProductivityProvider() {
    _loadSettings();
    _loadDataForDate(_selectedDate);
  }

  // Settings
  Future<void> _loadSettings() async {
    final firstDay = await _dbHelper.getSetting('firstDayOfWeek');
    if (firstDay != null) {
      _firstDayOfWeek = int.parse(firstDay);
    }
    notifyListeners();
  }

  Future<void> setFirstDayOfWeek(int day) async {
    _firstDayOfWeek = day;
    await _dbHelper.setSetting('firstDayOfWeek', day.toString());
    notifyListeners();
  }

  // Date management
  void setSelectedDate(DateTime date) {
    _selectedDate = date;
    _loadDataForDate(date);
    notifyListeners();
  }

  DateTime get weekStart {
    final daysFromFirstDay = (_selectedDate.weekday % 7 - _firstDayOfWeek) % 7;
    return _selectedDate.subtract(Duration(days: daysFromFirstDay));
  }

  List<DateTime> get weekDays {
    final start = weekStart;
    return List.generate(7, (index) => start.add(Duration(days: index)));
  }

  // Data loading
  Future<void> _loadDataForDate(DateTime date) async {
    _isLoading = true;
    notifyListeners();

    try {
      _tasks = await _dbHelper.getTasksForDate(date);
      _focusSessions = await _dbHelper.getFocusSessionsForDate(date);
      _dailyNote = await _dbHelper.getDailyNoteForDate(date);
    } catch (e) {
      debugPrint('Error loading data: $e');
    }

    _isLoading = false;
    notifyListeners();
  }

  Future<void> refreshData() async {
    await _loadDataForDate(_selectedDate);
  }

  // Task operations
  Future<void> addTask(String title, {String description = '', bool isPriority = false}) async {
    final task = Task(
      title: title,
      description: description,
      date: _selectedDate,
      isPriority: isPriority,
      order: _tasks.length,
      createdAt: DateTime.now(),
    );

    try {
      final id = await _dbHelper.insertTask(task);
      final newTask = task.copyWith(id: id);
      _tasks.add(newTask);
      notifyListeners();
    } catch (e) {
      debugPrint('Error adding task: $e');
    }
  }

  Future<void> updateTask(Task task) async {
    try {
      await _dbHelper.updateTask(task);
      final index = _tasks.indexWhere((t) => t.id == task.id);
      if (index != -1) {
        _tasks[index] = task;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error updating task: $e');
    }
  }

  Future<void> toggleTaskCompletion(Task task) async {
    final updatedTask = task.copyWith(
      isCompleted: !task.isCompleted,
      completedAt: !task.isCompleted ? DateTime.now() : null,
    );
    await updateTask(updatedTask);
  }

  Future<void> deleteTask(Task task) async {
    try {
      await _dbHelper.deleteTask(task.id!);
      _tasks.removeWhere((t) => t.id == task.id);
      notifyListeners();
    } catch (e) {
      debugPrint('Error deleting task: $e');
    }
  }

  Future<void> reorderTasks(int oldIndex, int newIndex) async {
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }
    
    final task = _tasks.removeAt(oldIndex);
    _tasks.insert(newIndex, task);
    
    // Update order in database
    for (int i = 0; i < _tasks.length; i++) {
      final updatedTask = _tasks[i].copyWith(order: i);
      _tasks[i] = updatedTask;
      await _dbHelper.updateTask(updatedTask);
    }
    
    notifyListeners();
  }

  // Focus session operations
  Future<void> addFocusSession(String title, int durationMinutes, {String description = ''}) async {
    final session = FocusSession(
      title: title,
      description: description,
      date: _selectedDate,
      durationMinutes: durationMinutes,
      createdAt: DateTime.now(),
    );

    try {
      final id = await _dbHelper.insertFocusSession(session);
      final newSession = session.copyWith(id: id);
      _focusSessions.add(newSession);
      notifyListeners();
    } catch (e) {
      debugPrint('Error adding focus session: $e');
    }
  }

  Future<void> startFocusSession(FocusSession session) async {
    final updatedSession = session.copyWith(
      startTime: DateTime.now(),
    );
    await updateFocusSession(updatedSession);
  }

  Future<void> completeFocusSession(FocusSession session, {String? accomplishments}) async {
    final updatedSession = session.copyWith(
      endTime: DateTime.now(),
      isCompleted: true,
      accomplishments: accomplishments,
    );
    await updateFocusSession(updatedSession);
  }

  Future<void> updateFocusSession(FocusSession session) async {
    try {
      await _dbHelper.updateFocusSession(session);
      final index = _focusSessions.indexWhere((s) => s.id == session.id);
      if (index != -1) {
        _focusSessions[index] = session;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error updating focus session: $e');
    }
  }

  Future<void> deleteFocusSession(FocusSession session) async {
    try {
      await _dbHelper.deleteFocusSession(session.id!);
      _focusSessions.removeWhere((s) => s.id == session.id);
      notifyListeners();
    } catch (e) {
      debugPrint('Error deleting focus session: $e');
    }
  }

  // Daily note operations
  Future<void> updateDailyNote(String content, {List<String> photos = const []}) async {
    final now = DateTime.now();
    final note = DailyNote(
      id: _dailyNote?.id,
      date: _selectedDate,
      content: content,
      photos: photos,
      createdAt: _dailyNote?.createdAt ?? now,
      updatedAt: now,
    );

    try {
      await _dbHelper.insertOrUpdateDailyNote(note);
      _dailyNote = note;
      notifyListeners();
    } catch (e) {
      debugPrint('Error updating daily note: $e');
    }
  }

  // Goal operations
  Future<void> loadGoals() async {
    try {
      final yearlyGoals = await _dbHelper.getGoalsByType(GoalType.yearly);
      final monthlyGoals = await _dbHelper.getGoalsByType(GoalType.monthly);
      final weeklyGoals = await _dbHelper.getGoalsByType(GoalType.weekly);
      
      _goals = [...yearlyGoals, ...monthlyGoals, ...weeklyGoals];
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading goals: $e');
    }
  }

  Future<void> addGoal(Goal goal) async {
    try {
      final id = await _dbHelper.insertGoal(goal);
      final newGoal = goal.copyWith(id: id);
      _goals.add(newGoal);
      notifyListeners();
    } catch (e) {
      debugPrint('Error adding goal: $e');
    }
  }

  Future<void> updateGoal(Goal goal) async {
    try {
      await _dbHelper.updateGoal(goal);
      final index = _goals.indexWhere((g) => g.id == goal.id);
      if (index != -1) {
        _goals[index] = goal;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error updating goal: $e');
    }
  }

  // Analytics
  Future<Map<String, dynamic>> getWeeklyStats() async {
    try {
      return await _dbHelper.getWeeklyStats(weekStart);
    } catch (e) {
      debugPrint('Error getting weekly stats: $e');
      return {};
    }
  }

  // Utility methods
  bool get isToday {
    final now = DateTime.now();
    return _selectedDate.year == now.year &&
           _selectedDate.month == now.month &&
           _selectedDate.day == now.day;
  }

  bool get isFriday => _selectedDate.weekday == DateTime.friday;

  List<Task> get completedTasks => _tasks.where((task) => task.isCompleted).toList();
  List<Task> get pendingTasks => _tasks.where((task) => !task.isCompleted).toList();
  List<Task> get priorityTasks => _tasks.where((task) => task.isPriority).toList();

  List<FocusSession> get completedSessions => 
      _focusSessions.where((session) => session.isCompleted).toList();
  
  int get totalFocusMinutesToday => 
      completedSessions.fold(0, (sum, session) => sum + session.durationMinutes);

  double get taskCompletionRate {
    if (_tasks.isEmpty) return 0.0;
    return completedTasks.length / _tasks.length;
  }
}
