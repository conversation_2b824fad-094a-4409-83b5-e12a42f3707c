import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/daily_note.dart';
import '../providers/productivity_provider.dart';

class DailyNotesWidget extends StatefulWidget {
  final DailyNote? note;

  const DailyNotesWidget({
    super.key,
    this.note,
  });

  @override
  State<DailyNotesWidget> createState() => _DailyNotesWidgetState();
}

class _DailyNotesWidgetState extends State<DailyNotesWidget> {
  late TextEditingController _controller;
  bool _isEditing = false;
  bool _hasChanges = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.note?.content ?? '');
    _controller.addListener(_onTextChanged);
  }

  @override
  void didUpdateWidget(DailyNotesWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.note?.content != oldWidget.note?.content) {
      _controller.text = widget.note?.content ?? '';
      _hasChanges = false;
    }
  }

  @override
  void dispose() {
    _controller.removeListener(_onTextChanged);
    _controller.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    final hasChanges = _controller.text != (widget.note?.content ?? '');
    if (hasChanges != _hasChanges) {
      setState(() {
        _hasChanges = hasChanges;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.note,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Daily Notes & Memories',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if (_hasChanges) ...[
                  TextButton(
                    onPressed: _saveNote,
                    child: const Text('Save'),
                  ),
                  const SizedBox(width: 8),
                ],
                IconButton(
                  icon: Icon(_isEditing ? Icons.visibility : Icons.edit),
                  onPressed: () {
                    setState(() {
                      _isEditing = !_isEditing;
                    });
                  },
                  tooltip: _isEditing ? 'Preview' : 'Edit',
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_isEditing) ...[
              TextField(
                controller: _controller,
                decoration: const InputDecoration(
                  hintText: 'Write about your day, ideas, memories, or anything you want to remember...',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.all(16),
                ),
                maxLines: 8,
                textInputAction: TextInputAction.newline,
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  OutlinedButton.icon(
                    onPressed: _addPhoto,
                    icon: const Icon(Icons.photo_camera),
                    label: const Text('Add Photo'),
                  ),
                  const SizedBox(width: 12),
                  OutlinedButton.icon(
                    onPressed: _addVoiceNote,
                    icon: const Icon(Icons.mic),
                    label: const Text('Voice Note'),
                  ),
                ],
              ),
            ] else ...[
              if (widget.note == null || widget.note!.content.isEmpty) ...[
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(32),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: Column(
                    children: [
                      Icon(
                        Icons.note_add,
                        size: 48,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'No notes for today',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Tap the edit button to start writing',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[500],
                        ),
                      ),
                    ],
                  ),
                ),
              ] else ...[
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    widget.note!.content,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      height: 1.5,
                    ),
                  ),
                ),
              ],
            ],

            // Photos section
            if (widget.note != null && widget.note!.photos.isNotEmpty) ...[
              const SizedBox(height: 16),
              Text(
                'Photos',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              _buildPhotosGrid(),
            ],

            // Metadata
            if (widget.note != null) ...[
              const SizedBox(height: 16),
              _buildMetadata(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPhotosGrid() {
    return SizedBox(
      height: 100,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: widget.note!.photos.length,
        itemBuilder: (context, index) {
          // final photoPath = widget.note!.photos[index];
          return Container(
            margin: const EdgeInsets.only(right: 8),
            width: 100,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Container(
                    width: 100,
                    height: 100,
                    color: Colors.grey[300],
                    child: const Icon(
                      Icons.photo,
                      size: 40,
                      color: Colors.grey,
                    ),
                  ),
                ),
                if (_isEditing)
                  Positioned(
                    top: 4,
                    right: 4,
                    child: GestureDetector(
                      onTap: () => _removePhoto(index),
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: const BoxDecoration(
                          color: Colors.red,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.close,
                          size: 16,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildMetadata() {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            size: 16,
            color: Colors.grey[600],
          ),
          const SizedBox(width: 8),
          Text(
            'Last updated: ${_formatDateTime(widget.note!.updatedAt)}',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const Spacer(),
          Text(
            '${widget.note!.content.length} characters',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${dateTime.day}/${dateTime.month} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
    }
  }

  void _saveNote() {
    final provider = context.read<ProductivityProvider>();
    provider.updateDailyNote(
      _controller.text,
      photos: widget.note?.photos ?? [],
    );
    setState(() {
      _hasChanges = false;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Note saved successfully'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _addPhoto() {
    // TODO: Implement photo picker
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Photo feature coming soon!'),
      ),
    );
  }

  void _addVoiceNote() {
    // TODO: Implement voice recording
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Voice note feature coming soon!'),
      ),
    );
  }

  void _removePhoto(int index) {
    final provider = context.read<ProductivityProvider>();
    final currentPhotos = List<String>.from(widget.note?.photos ?? []);
    currentPhotos.removeAt(index);

    provider.updateDailyNote(
      widget.note?.content ?? _controller.text,
      photos: currentPhotos,
    );
  }
}
