enum GoalType { yearly, monthly, weekly }

enum GoalStatus { onTrack, behind, completed, paused }

class Goal {
  final int? id;
  final String title;
  final String description;
  final GoalType type;
  final GoalStatus status;
  final double progress; // 0.0 to 1.0 (0% to 100%)
  final DateTime startDate;
  final DateTime endDate;
  final int? parentGoalId; // For hierarchical goals
  final DateTime createdAt;
  final DateTime updatedAt;

  Goal({
    this.id,
    required this.title,
    this.description = '',
    required this.type,
    this.status = GoalStatus.onTrack,
    this.progress = 0.0,
    required this.startDate,
    required this.endDate,
    this.parentGoalId,
    required this.createdAt,
    required this.updatedAt,
  });

  Goal copyWith({
    int? id,
    String? title,
    String? description,
    GoalType? type,
    GoalStatus? status,
    double? progress,
    DateTime? startDate,
    DateTime? endDate,
    int? parentGoalId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Goal(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      status: status ?? this.status,
      progress: progress ?? this.progress,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      parentGoalId: parentGoalId ?? this.parentGoalId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'type': type.name,
      'status': status.name,
      'progress': progress,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'parentGoalId': parentGoalId,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory Goal.fromMap(Map<String, dynamic> map) {
    return Goal(
      id: map['id'],
      title: map['title'],
      description: map['description'] ?? '',
      type: GoalType.values.firstWhere((e) => e.name == map['type']),
      status: GoalStatus.values.firstWhere((e) => e.name == map['status']),
      progress: map['progress']?.toDouble() ?? 0.0,
      startDate: DateTime.parse(map['startDate']),
      endDate: DateTime.parse(map['endDate']),
      parentGoalId: map['parentGoalId'],
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
    );
  }

  bool get isCompleted => status == GoalStatus.completed || progress >= 1.0;

  bool get isOverdue => DateTime.now().isAfter(endDate) && !isCompleted;

  int get progressPercentage => (progress * 100).round();

  @override
  String toString() {
    return 'Goal{id: $id, title: $title, type: $type, progress: $progressPercentage%}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Goal && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
