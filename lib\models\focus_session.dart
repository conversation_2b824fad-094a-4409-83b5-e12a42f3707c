class FocusSession {
  final int? id;
  final String title;
  final String description;
  final DateTime date;
  final int durationMinutes; // 20, 30, or 40 minutes
  final bool isCompleted;
  final DateTime? startTime;
  final DateTime? endTime;
  final String? accomplishments;
  final DateTime createdAt;

  FocusSession({
    this.id,
    required this.title,
    this.description = '',
    required this.date,
    required this.durationMinutes,
    this.isCompleted = false,
    this.startTime,
    this.endTime,
    this.accomplishments,
    required this.createdAt,
  });

  FocusSession copyWith({
    int? id,
    String? title,
    String? description,
    DateTime? date,
    int? durationMinutes,
    bool? isCompleted,
    DateTime? startTime,
    DateTime? endTime,
    String? accomplishments,
    DateTime? createdAt,
  }) {
    return FocusSession(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      date: date ?? this.date,
      durationMinutes: durationMinutes ?? this.durationMinutes,
      isCompleted: isCompleted ?? this.isCompleted,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      accomplishments: accomplishments ?? this.accomplishments,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'date': date.toIso8601String(),
      'durationMinutes': durationMinutes,
      'isCompleted': isCompleted ? 1 : 0,
      'startTime': startTime?.toIso8601String(),
      'endTime': endTime?.toIso8601String(),
      'accomplishments': accomplishments,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory FocusSession.fromMap(Map<String, dynamic> map) {
    return FocusSession(
      id: map['id'],
      title: map['title'],
      description: map['description'] ?? '',
      date: DateTime.parse(map['date']),
      durationMinutes: map['durationMinutes'],
      isCompleted: map['isCompleted'] == 1,
      startTime: map['startTime'] != null 
          ? DateTime.parse(map['startTime']) 
          : null,
      endTime: map['endTime'] != null 
          ? DateTime.parse(map['endTime']) 
          : null,
      accomplishments: map['accomplishments'],
      createdAt: DateTime.parse(map['createdAt']),
    );
  }

  Duration get actualDuration {
    if (startTime != null && endTime != null) {
      return endTime!.difference(startTime!);
    }
    return Duration.zero;
  }

  bool get isInProgress {
    return startTime != null && endTime == null;
  }

  @override
  String toString() {
    return 'FocusSession{id: $id, title: $title, duration: ${durationMinutes}min, isCompleted: $isCompleted}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FocusSession && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
