{"buildFiles": ["C:\\flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\mytodo\\week_management_system\\android\\app\\.cxx\\Debug\\3g2u735p\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\mytodo\\week_management_system\\android\\app\\.cxx\\Debug\\3g2u735p\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}