class DailyNote {
  final int? id;
  final DateTime date;
  final String content;
  final List<String> photos; // Photo paths
  final DateTime createdAt;
  final DateTime updatedAt;

  DailyNote({
    this.id,
    required this.date,
    required this.content,
    this.photos = const [],
    required this.createdAt,
    required this.updatedAt,
  });

  DailyNote copyWith({
    int? id,
    DateTime? date,
    String? content,
    List<String>? photos,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return DailyNote(
      id: id ?? this.id,
      date: date ?? this.date,
      content: content ?? this.content,
      photos: photos ?? this.photos,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'date': date.toIso8601String(),
      'content': content,
      'photos': photos.join(','), // Store as comma-separated string
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory DailyNote.fromMap(Map<String, dynamic> map) {
    return DailyNote(
      id: map['id'],
      date: DateTime.parse(map['date']),
      content: map['content'] ?? '',
      photos: map['photos'] != null && map['photos'].isNotEmpty
          ? map['photos'].split(',')
          : [],
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
    );
  }

  bool get isEmpty => content.trim().isEmpty && photos.isEmpty;

  @override
  String toString() {
    return 'DailyNote{id: $id, date: $date, content: ${content.length} chars, photos: ${photos.length}}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DailyNote && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
