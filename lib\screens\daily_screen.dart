import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../providers/productivity_provider.dart';
import '../widgets/task_list_widget.dart';
import '../widgets/focus_session_widget.dart';
import '../widgets/daily_notes_widget.dart';

class DailyScreen extends StatelessWidget {
  const DailyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ProductivityProvider>(
      builder: (context, provider, child) {
        return Scaffold(
          appBar: AppBar(
            title: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(_getDayName(provider.selectedDate)),
                Text(
                  DateFormat('MMMM d, yyyy').format(provider.selectedDate),
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
            actions: [
              if (provider.isFriday)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.secondary,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'Friday Tasks',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              IconButton(
                icon: const Icon(Icons.refresh),
                onPressed: () => provider.refreshData(),
              ),
            ],
          ),
          body: provider.isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Daily Summary
                      _buildDailySummary(context, provider),
                      const SizedBox(height: 24),

                      // Tasks Section
                      _buildSectionHeader(
                        context,
                        'Tasks',
                        Icons.task_alt,
                        onAdd: () => _showAddTaskDialog(context, provider),
                      ),
                      const SizedBox(height: 8),
                      TaskListWidget(tasks: provider.tasks),
                      const SizedBox(height: 24),

                      // Focus Sessions Section
                      _buildSectionHeader(
                        context,
                        'Focus Sessions',
                        Icons.timer,
                        onAdd: () => _showAddSessionDialog(context, provider),
                      ),
                      const SizedBox(height: 8),
                      FocusSessionWidget(sessions: provider.focusSessions),
                      const SizedBox(height: 24),

                      // Notes Section
                      _buildSectionHeader(
                        context,
                        'Daily Notes',
                        Icons.note,
                      ),
                      const SizedBox(height: 8),
                      DailyNotesWidget(note: provider.dailyNote),

                      // Friday Special Tasks
                      if (provider.isFriday) ...[
                        const SizedBox(height: 24),
                        _buildFridayTasks(context, provider),
                      ],
                    ],
                  ),
                ),
        );
      },
    );
  }

  String _getDayName(DateTime date) {
    // final arabicDays = ['الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
    final englishDays = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

    // For now using English, can be made configurable
    return englishDays[date.weekday % 7];
  }

  Widget _buildDailySummary(BuildContext context, ProductivityProvider provider) {
    final completionRate = provider.taskCompletionRate;
    final totalFocusTime = provider.totalFocusMinutesToday;
    final completionPercentage = (completionRate * 100).round();

    return Card(
      elevation: 6,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: _getProgressGradientColors(completionPercentage.toDouble()),
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.today,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Daily Progress',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '${provider.completedTasks.length} of ${provider.tasks.length} tasks completed',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.white.withOpacity(0.9),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(25),
                  ),
                  child: Text(
                    '$completionPercentage%',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: _getProgressGradientColors(completionPercentage.toDouble())[0],
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // Progress bar
            Container(
              height: 8,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.3),
                borderRadius: BorderRadius.circular(4),
              ),
              child: FractionallySizedBox(
                alignment: Alignment.centerLeft,
                widthFactor: completionRate,
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
            ),

            const SizedBox(height: 20),

            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    context,
                    'Tasks',
                    '${provider.completedTasks.length}/${provider.tasks.length}',
                    Icons.task_alt,
                    completionRate,
                    true, // isWhiteTheme
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    context,
                    'Focus Time',
                    '${totalFocusTime}min',
                    Icons.timer,
                    totalFocusTime > 0 ? 1.0 : 0.0,
                    true, // isWhiteTheme
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    context,
                    'Sessions',
                    '${provider.completedSessions.length}',
                    Icons.play_circle,
                    provider.completedSessions.isNotEmpty ? 1.0 : 0.0,
                    true, // isWhiteTheme
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<Color> _getProgressGradientColors(double percentage) {
    if (percentage >= 80) {
      return [const Color(0xFF4CAF50), const Color(0xFF8BC34A)]; // Green
    } else if (percentage >= 60) {
      return [const Color(0xFF2196F3), const Color(0xFF03DAC6)]; // Blue
    } else if (percentage >= 40) {
      return [const Color(0xFFFF9800), const Color(0xFFFFEB3B)]; // Orange
    } else if (percentage >= 20) {
      return [const Color(0xFFFF5722), const Color(0xFFFF9800)]; // Red-Orange
    } else {
      return [const Color(0xFF9E9E9E), const Color(0xFFBDBDBD)]; // Grey
    }
  }

  Widget _buildSummaryItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    double progress, [
    bool isWhiteTheme = false,
  ]) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isWhiteTheme
            ? Colors.white.withOpacity(0.2)
            : Theme.of(context).colorScheme.surfaceVariant,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: isWhiteTheme
                ? Colors.white
                : Theme.of(context).colorScheme.primary,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: isWhiteTheme ? Colors.white : null,
            ),
          ),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: isWhiteTheme ? Colors.white.withOpacity(0.9) : null,
            ),
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: isWhiteTheme
                ? Colors.white.withOpacity(0.3)
                : Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(
              isWhiteTheme
                  ? Colors.white
                  : progress >= 0.8 ? Colors.green :
                    progress >= 0.5 ? Colors.orange : Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(
    BuildContext context,
    String title,
    IconData icon, {
    VoidCallback? onAdd,
  }) {
    return Row(
      children: [
        Icon(icon, color: Theme.of(context).colorScheme.primary),
        const SizedBox(width: 8),
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        if (onAdd != null)
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: onAdd,
          ),
      ],
    );
  }

  Widget _buildFridayTasks(BuildContext context, ProductivityProvider provider) {
    return Card(
      color: Theme.of(context).colorScheme.secondaryContainer,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.star,
                  color: Theme.of(context).colorScheme.secondary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Friday Special Tasks',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ListTile(
              leading: const Icon(Icons.assessment),
              title: const Text('تقييم الأسبوع الماضي (Weekly Review)'),
              trailing: Checkbox(
                value: false, // TODO: Implement Friday task tracking
                onChanged: (value) {},
              ),
            ),
            ListTile(
              leading: const Icon(Icons.event_note),
              title: const Text('التخطيط للأسبوع القادم (Next Week Planning)'),
              trailing: Checkbox(
                value: false, // TODO: Implement Friday task tracking
                onChanged: (value) {},
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showAddTaskDialog(BuildContext context, ProductivityProvider provider) {
    final titleController = TextEditingController();
    final descriptionController = TextEditingController();
    bool isPriority = false;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Add Task'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: titleController,
                decoration: const InputDecoration(
                  labelText: 'Task Title',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 12),
              TextField(
                controller: descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description (Optional)',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
              const SizedBox(height: 12),
              CheckboxListTile(
                title: const Text('Priority Task'),
                value: isPriority,
                onChanged: (value) => setState(() => isPriority = value ?? false),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                if (titleController.text.isNotEmpty) {
                  provider.addTask(
                    titleController.text,
                    description: descriptionController.text,
                    isPriority: isPriority,
                  );
                  Navigator.pop(context);
                }
              },
              child: const Text('Add'),
            ),
          ],
        ),
      ),
    );
  }

  void _showAddSessionDialog(BuildContext context, ProductivityProvider provider) {
    final titleController = TextEditingController();
    final descriptionController = TextEditingController();
    int selectedDuration = 25;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Add Focus Session'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: titleController,
                decoration: const InputDecoration(
                  labelText: 'Session Title',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 12),
              TextField(
                controller: descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description (Optional)',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
              const SizedBox(height: 12),
              DropdownButtonFormField<int>(
                value: selectedDuration,
                decoration: const InputDecoration(
                  labelText: 'Duration',
                  border: OutlineInputBorder(),
                ),
                items: const [
                  DropdownMenuItem(value: 20, child: Text('20 minutes')),
                  DropdownMenuItem(value: 25, child: Text('25 minutes')),
                  DropdownMenuItem(value: 30, child: Text('30 minutes')),
                  DropdownMenuItem(value: 40, child: Text('40 minutes')),
                ],
                onChanged: (value) => setState(() => selectedDuration = value ?? 25),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                if (titleController.text.isNotEmpty) {
                  provider.addFocusSession(
                    titleController.text,
                    selectedDuration,
                    description: descriptionController.text,
                  );
                  Navigator.pop(context);
                }
              },
              child: const Text('Add'),
            ),
          ],
        ),
      ),
    );
  }
}
