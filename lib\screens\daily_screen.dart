import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../providers/productivity_provider.dart';
import '../widgets/task_list_widget.dart';
import '../widgets/focus_session_widget.dart';
import '../widgets/daily_notes_widget.dart';
import 'weekly_view.dart';

class DailyScreen extends StatelessWidget {
  const DailyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ProductivityProvider>(
      builder: (context, provider, child) {
        return Scaffold(
          appBar: AppBar(
            elevation: 0,
            backgroundColor: Colors.transparent,
            flexibleSpace: Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [Color(0xFF667eea), Color(0xFF764ba2)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
            ),
            leading: Container(
              margin: const EdgeInsets.only(left: 8),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.white),
                onPressed: () {
                  if (Navigator.canPop(context)) {
                    Navigator.pop(context);
                  } else {
                    // If there's no previous screen, navigate to weekly view
                    Navigator.pushReplacement(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const WeeklyView(),
                      ),
                    );
                  }
                },
                tooltip: 'Back to Weekly View',
              ),
            ),
            title: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getDayName(provider.selectedDate),
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
                Text(
                  DateFormat('MMMM d, yyyy').format(provider.selectedDate),
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
            actions: [
              // Weekly View Button
              Container(
                margin: const EdgeInsets.only(right: 4),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: IconButton(
                  icon: const Icon(Icons.calendar_view_week, color: Colors.white),
                  onPressed: () {
                    Navigator.pushReplacement(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const WeeklyView(),
                      ),
                    );
                  },
                  tooltip: 'Weekly View',
                ),
              ),

              // Friday Tasks Badge
              if (provider.isFriday)
                Container(
                  margin: const EdgeInsets.only(right: 4),
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.amber,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.star, color: Colors.white, size: 14),
                      SizedBox(width: 4),
                      Text(
                        'Friday',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),

              // Refresh Button
              Container(
                margin: const EdgeInsets.only(right: 8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: IconButton(
                  icon: const Icon(Icons.refresh, color: Colors.white),
                  onPressed: () => provider.refreshData(),
                  tooltip: 'Refresh',
                ),
              ),
            ],
          ),
          body: provider.isLoading
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 20,
                              offset: const Offset(0, 10),
                            ),
                          ],
                        ),
                        child: const CircularProgressIndicator(
                          strokeWidth: 3,
                          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF667eea)),
                        ),
                      ),
                      const SizedBox(height: 20),
                      Text(
                        'Loading your day...',
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.8),
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                )
              : Container(
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Color(0xFFF8F9FA),
                        Color(0xFFE9ECEF),
                      ],
                    ),
                  ),
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Daily Summary
                        _buildDailySummary(context, provider),
                        const SizedBox(height: 24),

                        // Quick Actions Row
                        _buildQuickActions(context, provider),
                        const SizedBox(height: 24),

                        // Tasks Section
                        _buildModernSectionCard(
                          context,
                          'Today\'s Tasks',
                          Icons.task_alt,
                          const Color(0xFF4CAF50),
                          TaskListWidget(tasks: provider.tasks),
                          onAdd: () => _showAddTaskDialog(context, provider),
                        ),
                        const SizedBox(height: 20),

                        // Focus Sessions Section
                        _buildModernSectionCard(
                          context,
                          'Focus Sessions',
                          Icons.timer,
                          const Color(0xFF2196F3),
                          FocusSessionWidget(sessions: provider.focusSessions),
                          onAdd: () => _showAddSessionDialog(context, provider),
                        ),
                        const SizedBox(height: 20),

                        // Notes Section
                        _buildModernSectionCard(
                          context,
                          'Daily Notes & Memories',
                          Icons.note,
                          const Color(0xFFFF9800),
                          DailyNotesWidget(note: provider.dailyNote),
                        ),

                        // Friday Special Tasks
                        if (provider.isFriday) ...[
                          const SizedBox(height: 20),
                          _buildFridayTasks(context, provider),
                        ],

                        // Bottom spacing for FAB
                        const SizedBox(height: 100),
                      ],
                    ),
                  ),
                ),
          floatingActionButton: _buildNavigationFAB(context, provider),
          floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
        );
      },
    );
  }

  String _getDayName(DateTime date) {
    // final arabicDays = ['الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
    final englishDays = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

    // For now using English, can be made configurable
    return englishDays[date.weekday % 7];
  }

  Widget _buildDailySummary(BuildContext context, ProductivityProvider provider) {
    final completionRate = provider.taskCompletionRate;
    final totalFocusTime = provider.totalFocusMinutesToday;
    final completionPercentage = (completionRate * 100).round();

    return Card(
      elevation: 6,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: _getProgressGradientColors(completionPercentage.toDouble()),
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.today,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Daily Progress',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '${provider.completedTasks.length} of ${provider.tasks.length} tasks completed',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.white.withOpacity(0.9),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(25),
                  ),
                  child: Text(
                    '$completionPercentage%',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: _getProgressGradientColors(completionPercentage.toDouble())[0],
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // Progress bar
            Container(
              height: 8,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.3),
                borderRadius: BorderRadius.circular(4),
              ),
              child: FractionallySizedBox(
                alignment: Alignment.centerLeft,
                widthFactor: completionRate,
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
            ),

            const SizedBox(height: 20),

            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    context,
                    'Tasks',
                    '${provider.completedTasks.length}/${provider.tasks.length}',
                    Icons.task_alt,
                    completionRate,
                    true, // isWhiteTheme
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    context,
                    'Focus Time',
                    '${totalFocusTime}min',
                    Icons.timer,
                    totalFocusTime > 0 ? 1.0 : 0.0,
                    true, // isWhiteTheme
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    context,
                    'Sessions',
                    '${provider.completedSessions.length}',
                    Icons.play_circle,
                    provider.completedSessions.isNotEmpty ? 1.0 : 0.0,
                    true, // isWhiteTheme
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<Color> _getProgressGradientColors(double percentage) {
    if (percentage >= 80) {
      return [const Color(0xFF4CAF50), const Color(0xFF8BC34A)]; // Green
    } else if (percentage >= 60) {
      return [const Color(0xFF2196F3), const Color(0xFF03DAC6)]; // Blue
    } else if (percentage >= 40) {
      return [const Color(0xFFFF9800), const Color(0xFFFFEB3B)]; // Orange
    } else if (percentage >= 20) {
      return [const Color(0xFFFF5722), const Color(0xFFFF9800)]; // Red-Orange
    } else {
      return [const Color(0xFF9E9E9E), const Color(0xFFBDBDBD)]; // Grey
    }
  }

  Widget _buildSummaryItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    double progress, [
    bool isWhiteTheme = false,
  ]) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isWhiteTheme
            ? Colors.white.withOpacity(0.2)
            : Theme.of(context).colorScheme.surfaceVariant,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: isWhiteTheme
                ? Colors.white
                : Theme.of(context).colorScheme.primary,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: isWhiteTheme ? Colors.white : null,
            ),
          ),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: isWhiteTheme ? Colors.white.withOpacity(0.9) : null,
            ),
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: isWhiteTheme
                ? Colors.white.withOpacity(0.3)
                : Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(
              isWhiteTheme
                  ? Colors.white
                  : progress >= 0.8 ? Colors.green :
                    progress >= 0.5 ? Colors.orange : Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context, ProductivityProvider provider) {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () {
              debugPrint('Add Task button tapped!');
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Add Task button works!'),
                  duration: Duration(seconds: 1),
                ),
              );
              _showAddTaskDialog(context, provider);
            },
            icon: const Icon(Icons.add_task),
            label: const Text('Add Task'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4CAF50),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.all(16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildQuickActionCard(
            context,
            'Start Focus',
            Icons.play_circle_fill,
            const Color(0xFF2196F3),
            () => _showAddSessionDialog(context, provider),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () async {
              debugPrint('Debug Database button tapped!');
              await provider.debugDatabaseInfo();
              if (context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Database info printed to console'),
                    duration: Duration(seconds: 2),
                  ),
                );
              }
            },
            icon: const Icon(Icons.bug_report),
            label: const Text('Debug DB'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFF9800),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.all(16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildQuickActionCard(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: () {
        debugPrint('Quick action card tapped: $title');
        onTap();
      },
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [color, color.withOpacity(0.8)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: color.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernSectionCard(
    BuildContext context,
    String title,
    IconData icon,
    Color accentColor,
    Widget content, {
    VoidCallback? onAdd,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [accentColor, accentColor.withOpacity(0.8)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(
                    icon,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                if (onAdd != null)
                  GestureDetector(
                    onTap: onAdd,
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: const Icon(
                        Icons.add,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                  ),
              ],
            ),
          ),
          // Content
          Padding(
            padding: const EdgeInsets.all(16),
            child: content,
          ),
        ],
      ),
    );
  }

  Widget _buildFridayTasks(BuildContext context, ProductivityProvider provider) {
    return Container(
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF9C27B0), Color(0xFF673AB7)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF9C27B0).withOpacity(0.3),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.star,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    'Friday Special Tasks',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: const Text(
                    'FRIDAY',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            _buildFridayTaskItem(
              context,
              Icons.assessment,
              'تقييم الأسبوع الماضي',
              'Weekly Review',
              false,
            ),
            const SizedBox(height: 12),
            _buildFridayTaskItem(
              context,
              Icons.event_note,
              'التخطيط للأسبوع القادم',
              'Next Week Planning',
              false,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFridayTaskItem(
    BuildContext context,
    IconData icon,
    String arabicTitle,
    String englishTitle,
    bool isCompleted,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  arabicTitle,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  englishTitle,
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.8),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Transform.scale(
            scale: 1.2,
            child: Checkbox(
              value: isCompleted,
              onChanged: (value) {
                // TODO: Implement Friday task tracking
              },
              fillColor: WidgetStateProperty.all(Colors.white),
              checkColor: const Color(0xFF9C27B0),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationFAB(BuildContext context, ProductivityProvider provider) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Weekly View FAB
        FloatingActionButton(
          heroTag: "weekly_view",
          onPressed: () {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(
                builder: (context) => const WeeklyView(),
              ),
            );
          },
          backgroundColor: const Color(0xFF667eea),
          tooltip: 'Weekly View',
          child: const Icon(Icons.calendar_view_week, color: Colors.white),
        ),

        const SizedBox(height: 12),

        // Date Navigation FAB
        FloatingActionButton.small(
          heroTag: "date_nav",
          onPressed: () => _showDatePicker(context, provider),
          backgroundColor: Colors.white,
          tooltip: 'Select Date',
          child: const Icon(Icons.date_range, color: Color(0xFF667eea)),
        ),
      ],
    );
  }

  void _showDatePicker(BuildContext context, ProductivityProvider provider) {
    showDatePicker(
      context: context,
      initialDate: provider.selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Color(0xFF667eea),
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    ).then((selectedDate) {
      if (selectedDate != null) {
        provider.setSelectedDate(selectedDate);
      }
    });
  }

  void _showAddTaskDialog(BuildContext context, ProductivityProvider provider) {
    debugPrint('_showAddTaskDialog called!');
    final titleController = TextEditingController();
    final descriptionController = TextEditingController();
    bool isPriority = false;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Add New Task'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: titleController,
                decoration: const InputDecoration(
                  labelText: 'Task Title',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description (Optional)',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 16),
              CheckboxListTile(
                title: const Text('Priority Task'),
                value: isPriority,
                onChanged: (value) => setState(() => isPriority = value ?? false),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                print('=== DIALOG ADD TASK BUTTON PRESSED ===');
                print('Title: ${titleController.text}');
                print('Description: ${descriptionController.text}');
                print('Is Priority: $isPriority');

                if (titleController.text.isNotEmpty) {
                  print('Title is not empty, calling provider.addTask...');
                  await provider.addTask(
                    titleController.text,
                    description: descriptionController.text,
                    isPriority: isPriority,
                  );
                  print('provider.addTask completed, closing dialog...');
                  if (context.mounted) Navigator.pop(context);
                  print('Dialog closed');
                } else {
                  print('Title is empty, not adding task');
                }
                print('=== DIALOG ADD TASK BUTTON COMPLETE ===');
              },
              child: const Text('Add Task'),
            ),
          ],
        ),
      ),
    );
  }

  void _showAddSessionDialog(BuildContext context, ProductivityProvider provider) {
    final titleController = TextEditingController();
    final descriptionController = TextEditingController();
    int selectedDuration = 25;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Add Focus Session'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: titleController,
                decoration: const InputDecoration(
                  labelText: 'Session Title',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description (Optional)',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<int>(
                value: selectedDuration,
                decoration: const InputDecoration(
                  labelText: 'Duration',
                  border: OutlineInputBorder(),
                ),
                items: [20, 25, 30, 45, 60, 90].map((duration) {
                  return DropdownMenuItem(
                    value: duration,
                    child: Text('$duration minutes'),
                  );
                }).toList(),
                onChanged: (value) => setState(() => selectedDuration = value ?? 25),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (titleController.text.isNotEmpty) {
                  await provider.addFocusSession(
                    titleController.text,
                    selectedDuration,
                    description: descriptionController.text,
                  );
                  if (context.mounted) Navigator.pop(context);
                }
              },
              child: const Text('Add Session'),
            ),
          ],
        ),
      ),
    );
  }
}
